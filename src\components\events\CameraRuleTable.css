.camera-rule-table {
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
  color: #f0f0f0;
  padding: 20px;
}

.zone-selector-container {
  margin-bottom: 30px;
}

.zone-selector-container h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #fff;
}

.zone-dropdown {
  max-width: 100%;
  width: 100%;
}

.zone-select {
  width: 100%;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #444;
  background-color: #2c2c2c;
  color: #000000;
  font-size: 12px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 12px;
}

/* Zone cameras container */
.zone-cameras-container {
  margin-top: 12px;
}

.zone-cameras-container h3 {
  font-size: 14px;
  margin-bottom: 8px;
  color: #000000;
  font-weight: 600;
}

/* Camera rules list */
.camera-rules-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.camera-rule-item {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  border: 1px solid #e0e0e0;
}

.camera-name {
  display: flex;
  align-items: center;
}

.camera-name span {
  font-size: 12px;
  font-weight: 600;
  color: #000000;
}

.applied-rules {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.applied-rules h4 {
  font-size: 11px;
  color: #666666;
  margin: 0;
  font-weight: 600;
}

/* Detection checkboxes */
.detection-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 3px;
}

.detection-checkbox-container {
  position: relative;
}

.detection-checkbox-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.detection-checkbox {
  width: 12px;
  height: 12px;
  accent-color: #000000;
  margin-bottom: 2px;
}

.detection-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  color: #000000;
  transition: all 0.2s;
  border: 1px solid #dee2e6;
}

.detection-checkbox:checked + .detection-number {
  background-color: #000000;
  color: #ffffff;
  border: 1px solid #000000;
}

/* Tooltip */
.detection-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  pointer-events: none;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(5px);
}

.detection-tooltip::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 5px 5px;
  border-style: solid;
  border-color: transparent transparent rgba(0, 0, 0, 0.8);
}

.no-cameras {
  text-align: center;
  padding: 30px;
  color: #888;
  font-style: italic;
  background-color: #2c2c2c;
  border-radius: 8px;
}
