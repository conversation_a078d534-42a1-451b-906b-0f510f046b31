.rule-toolbar {
  background-color: #2c2c2c;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.rule-toolbar-header {
  margin-bottom: 12px;
}

.rule-toolbar-header h3 {
  font-size: 18px;
  margin-bottom: 4px;
  color: #b6e14b;
}

.rule-toolbar-header p {
  font-size: 13px;
  color: #aaa;
  margin: 0;
}

.rule-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
  gap: 10px;
  margin-bottom: 16px;
}

.rule-button {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rule-button.enabled {
  background-color: #3a3a3a;
  color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.rule-button.enabled:hover {
  background-color: #4a4a4a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.rule-button.disabled {
  background-color: #2a2a2a;
  color: #666;
  cursor: not-allowed;
  opacity: 0.7;
}

.rule-button.selected {
  background-color: #b6e14b;
  color: #1a1a1a;
}

.rule-button.selected:hover {
  background-color: #333333;
}

.selected-rules {
  margin-top: 8px;
  padding: 6px;
  background-color: #f8f9fa;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
  border: 1px solid #e0e0e0;
}

.selected-rules > span {
  font-weight: 600;
  color: #666666;
  margin-right: 4px;
  font-size: 11px;
}

.no-rules-selected {
  color: #999999;
  font-style: italic;
  font-size: 11px;
}

.selected-rule-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.rule-tag {
  background-color: #000000;
  color: #ffffff;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.remove-rule {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 3px;
  padding: 0 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.remove-rule:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
