.rule-toolbar {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

.rule-toolbar-header {
  margin-bottom: 12px;
}

.rule-toolbar-header h3 {
  font-size: 18px;
  margin-bottom: 4px;
  color: #000000;
}

.rule-toolbar-header p {
  font-size: 13px;
  color: #666666;
  margin: 0;
}

.rule-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
  gap: 10px;
  margin-bottom: 16px;
}

.rule-button {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rule-button.enabled {
  background-color: #f8f9fa;
  color: #000000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.rule-button.enabled:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.rule-button.disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
  border: 1px solid #dee2e6;
}

.rule-button.selected {
  background-color: #000000;
  color: #ffffff;
}

.rule-button.selected:hover {
  background-color: #333333;
}

.selected-rules {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 8px;
  border: 1px solid #e0e0e0;
}

.selected-rules > span {
  font-weight: 600;
  color: #666666;
  margin-right: 8px;
}

.no-rules-selected {
  color: #6c757d;
  font-style: italic;
}

.selected-rule-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.rule-tag {
  background-color: #000000;
  color: #ffffff;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.remove-rule {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 6px;
  padding: 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.remove-rule:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
