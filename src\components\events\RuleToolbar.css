.rule-toolbar {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
}

.rule-toolbar-header {
  margin-bottom: 6px;
}

.rule-toolbar-header h3 {
  font-size: 14px;
  margin-bottom: 2px;
  color: #000000;
  font-weight: 600;
}

.rule-toolbar-header p {
  font-size: 11px;
  color: #666666;
  margin: 0;
}

.rule-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 6px;
  margin-bottom: 8px;
}

.rule-button {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rule-button.enabled {
  background-color: #f8f9fa;
  color: #000000;
  border: 1px solid #e0e0e0;
}

.rule-button.enabled:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rule-button.disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
  border: 1px solid #dee2e6;
}

.rule-button.selected {
  background-color: #000000;
  color: #ffffff;
  border: 1px solid #000000;
}

.rule-button.selected:hover {
  background-color: #c8f35c;
}

.selected-rules {
  margin-top: 16px;
  padding: 12px;
  background-color: #222;
  border-radius: 6px;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-rules > span {
  font-weight: 600;
  color: #aaa;
  margin-right: 8px;
}

.no-rules-selected {
  color: #666;
  font-style: italic;
}

.selected-rule-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.rule-tag {
  background-color: #b6e14b;
  color: #1a1a1a;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.remove-rule {
  background: none;
  border: none;
  color: #1a1a1a;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 6px;
  padding: 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.remove-rule:hover {
  background-color: rgba(0, 0, 0, 0.2);
}
