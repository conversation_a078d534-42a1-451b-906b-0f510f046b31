.rules-on-camera {
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  color: #000000;
}

.rules-on-camera-header {
  margin-bottom: 20px;
}

.rules-on-camera-header h2 {
  font-size: 24px;
  margin-bottom: 8px;
  color: #000000;
}

.rules-on-camera-header p {
  font-size: 14px;
  color: #666666;
}

.rules-on-camera-filters {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
  flex-wrap: wrap;
}

.search-filter {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #ffffff;
  color: #000000;
  font-size: 14px;
}

.area-filter {
  width: 180px;
}

.area-select {
  width: 100%;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #ffffff;
  color: #000000;
  font-size: 14px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
}

.select-all-container {
  display: flex;
  align-items: center;
}

.select-all-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.select-all-checkbox {
  margin-right: 8px;
  width: 18px;
  height: 18px;
  accent-color: #000000;
}

.filter-apply-button {
  padding: 10px 16px;
  background-color: #000000;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.filter-apply-button:hover {
  background-color: #333333;
}

.filter-apply-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.success-message {
  background-color: rgba(0, 0, 0, 0.1);
  border-left: 4px solid #000000;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #000000;
}

.error-message {
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 4px solid #dc3545;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #dc3545;
}

.rules-on-camera-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #666666;
}
